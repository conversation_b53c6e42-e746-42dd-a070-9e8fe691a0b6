.setup-content{
    position: relative;
    &:hover{
        .setup-box{
            display: block;
        }
    }
}

.setup-box{
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, .2);
    z-index: 99;
    width: 100%;
    height: 100%;
    .ivu-btn{
        float: right;
        margin-right: 5px;
        margin-top: 5px;
    }
}

.modal-tab-bar{
    margin-left: 20px;
    table{
        margin-top: 10px;
        display:inline-block;
        border: 1px solid #ddd;
        border-radius: 5px;
        max-height: 400px !important;
        overflow: hidden auto;
    }
    thead{
        background-color: #eee;
        th{
            padding: 5px 0;
        }
    }
    td{
       padding: 10px;
       text-align: center;
    }
    tbody>tr:hover{
        background-color: aliceblue;
    }
}

.modal-top-advert{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    >*{
        margin-bottom: 10px;
    }
    .ivu-input-wrapper{
        width: 200px;
    }
}