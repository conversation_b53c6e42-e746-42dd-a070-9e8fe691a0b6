<template>
  <div class="layout">
    <Form ref="formValidate" :label-width="150" label-position="right" :model="formValidate" :rules="ruleValidate">
      
      <!-- 启用OCR服务 -->
      <FormItem label="启用OCR服务" prop="enabled">
        <i-switch v-model="formValidate.enabled" size="large">
          <span slot="open">开启</span>
          <span slot="close">关闭</span>
        </i-switch>
        <span class="desc">控制是否启用OCR识别功能</span>
      </FormItem>

      <!-- 服务类型 -->
      <FormItem label="服务类型" prop="type">
        <Select v-model="formValidate.type" style="width: 200px;">
          <Option value="ALIYUN">阿里云OCR</Option>
        </Select>
        <span class="desc">当前仅支持阿里云，预留扩展其他服务商</span>
      </FormItem>

      <!-- AccessKey ID -->
      <FormItem label="AccessKey ID" prop="accessKeyId">
        <Input 
          v-model="formValidate.accessKeyId" 
          placeholder="请输入阿里云AccessKey ID"
          style="width: 300px;"
        />
        <span class="desc">用于API认证的访问密钥ID</span>
      </FormItem>

      <!-- AccessKey Secret -->
      <FormItem label="AccessKey Secret" prop="accessKeySecret">
        <Input 
          v-model="formValidate.accessKeySecret" 
          :type="showSecret ? 'text' : 'password'"
          placeholder="请输入阿里云AccessKey Secret"
          style="width: 300px;"
        />
        <Button 
          type="text" 
          icon="ios-eye" 
          @click="toggleSecretVisibility"
          style="margin-left: 8px;"
        >
          {{ showSecret ? '隐藏' : '显示' }}
        </Button>
        <span class="desc">用于API认证的访问密钥，请妥善保管</span>
      </FormItem>

      <!-- 区域设置 -->
      <FormItem label="区域设置" prop="regionId">
        <Select v-model="formValidate.regionId" style="width: 200px;">
          <Option value="cn-hangzhou">华东1（杭州）</Option>
          <Option value="cn-shanghai">华东2（上海）</Option>
          <Option value="cn-beijing">华北2（北京）</Option>
          <Option value="cn-shenzhen">华南1（深圳）</Option>
          <Option value="cn-guangzhou">华南2（广州）</Option>
        </Select>
        <span class="desc">选择阿里云OCR服务的区域节点</span>
      </FormItem>

      <!-- 保存按钮 -->
      <div class="label-btns">
        <Button type="primary" @click="submit('formValidate')" :loading="submitLoading">
          保存配置
        </Button>
      </div>
    </Form>
  </div>
</template>

<script>
import { setSetting } from "@/api/index";
import { handleSubmit } from "./validate";

export default {
  name: "OCR_SETTING",
  title: "OCR识别设置",
  props: {
    res: {
      type: null,
      default: ""
    },
    type: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      submitLoading: false, // 提交加载状态
      showSecret: false, // 是否显示密钥
      ruleValidate: {}, // 验证规则
      formValidate: { // 表单数据
        enabled: true,
        type: "ALIYUN",
        accessKeyId: "",
        accessKeySecret: "",
        regionId: "cn-hangzhou"
      },
      result: ""
    };
  },
  created() {
    this.init();
  },
  methods: {
    // 切换密钥显示/隐藏
    toggleSecretVisibility() {
      this.showSecret = !this.showSecret;
    },
    
    // 保存
    submit(name) {
      let that = this;
      if (handleSubmit(that, name)) {
        this.setupSetting();
      }
    },
    
    // 保存设置
    setupSetting() {
      this.submitLoading = true;
      setSetting(this.type, this.formValidate).then((res) => {
        this.submitLoading = false;
        if (res.success) {
          this.$Message.success("OCR配置保存成功!");
        } else {
          this.$Message.error(res.message || "保存失败!");
        }
      }).catch((error) => {
        this.submitLoading = false;
        this.$Message.error("网络错误，请稍后重试");
      });
    },
    
    // 实例化数据
    init() {
      if (this.res) {
        this.result = JSON.parse(this.res);
        
        // 设置表单默认值
        this.$set(this, "formValidate", { ...this.formValidate, ...this.result });
      }
      
      // 设置验证规则
      this.ruleValidate = {
        enabled: [
          {
            required: true,
            type: 'boolean',
            message: "请选择是否启用OCR服务",
            trigger: "change",
          }
        ],
        type: [
          {
            required: true,
            message: "请选择服务类型",
            trigger: "change",
          }
        ],
        accessKeyId: [
          {
            required: true,
            message: "请输入AccessKey ID",
            trigger: "blur",
          },
          {
            min: 16,
            max: 30,
            message: "AccessKey ID长度应为16-30位",
            trigger: "blur",
          },
          {
            pattern: /^[A-Za-z0-9]+$/,
            message: "AccessKey ID只能包含字母和数字",
            trigger: "blur",
          }
        ],
        accessKeySecret: [
          {
            required: true,
            message: "请输入AccessKey Secret",
            trigger: "blur",
          },
          {
            min: 30,
            max: 50,
            message: "AccessKey Secret长度应为30-50位",
            trigger: "blur",
          }
        ],
        regionId: [
          {
            required: true,
            message: "请选择区域",
            trigger: "change",
          }
        ]
      };
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./style.scss";

.label-item {
  display: flex;
}

/deep/ .ivu-input {
  width: 300px !important;
}

/deep/ .ivu-select {
  width: 200px !important;
}
</style>
